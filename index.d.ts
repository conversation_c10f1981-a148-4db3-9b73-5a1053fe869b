/// <reference types="vite/client" />

interface DatabaseResult {
  success: boolean
  message?: string
  data?: any
  error?: string
  rowCount?: number
}

interface ElectronAPI {
  testDbConnection(): Promise<DatabaseResult>
  getTables(): Promise<DatabaseResult>
  executeQuery(query: string, params?: any[]): Promise<DatabaseResult>
  fetchData(): Promise<any>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
