/// <reference types="vite/client" />

interface DatabaseResult {
  success: boolean
  message?: string
  data?: any
  error?: string
  rowCount?: number
}

interface User {
  id: number
  username: string
  created_at: string
}

interface LoginResult {
  success: boolean
  message?: string
  user?: User
  error?: string
}

interface ElectronAPI {
  testDbConnection(): Promise<DatabaseResult>
  getTables(): Promise<DatabaseResult>
  executeQuery(query: string, params?: any[]): Promise<DatabaseResult>
  fetchData(): Promise<any>
  login(username: string, password: string): Promise<LoginResult>
  getUserProfile(userId: number): Promise<LoginResult>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
