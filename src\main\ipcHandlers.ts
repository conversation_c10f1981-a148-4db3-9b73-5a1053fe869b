import axios from 'axios';
import { ipcMain } from 'electron';
import { Pool } from 'pg';

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

// Database configuration
const dbConfig = {
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require',
  ssl: {
    rejectUnauthorized: false
  }
};

// Create a connection pool
const pool = new Pool(dbConfig);

export function setupIpcHandlers() {
  console.log('Setting up IPC handlers...');

  // Test database connection
  ipcMain.handle('test-db-connection', async () => {
    try {
      console.log('Testing database connection...');
      const client = await pool.connect();

      // Test with a simple query
      const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
      client.release();

      console.log('Database connection successful!');
      return {
        success: true,
        message: 'Database connection successful',
        data: result.rows[0]
      };
    } catch (error) {
      console.error('Database connection failed:', error);
      return {
        success: false,
        message: 'Database connection failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Execute custom SQL queries
  ipcMain.handle('execute-query', async (event, query: string, params: any[] = []) => {
    try {
      console.log('Executing query:', query);
      const client = await pool.connect();
      const result = await client.query(query, params);
      client.release();

      return {
        success: true,
        data: result.rows,
        rowCount: result.rowCount
      };
    } catch (error) {
      console.error('Query execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get database tables
  ipcMain.handle('get-tables', async () => {
    try {
      console.log('Fetching database tables...');
      const client = await pool.connect();
      const result = await client.query(`
        SELECT table_name, table_schema
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY table_name
      `);
      client.release();

      return {
        success: true,
        data: result.rows
      };
    } catch (error) {
      console.error('Failed to fetch tables:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  ipcMain.handle('fetch-data', async () => {
    try {
      console.log('fetch-data handler called');
      const response = await axios.get<Todo[]>('https://jsonplaceholder.typicode.com/todos');
      console.log('API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  });

  console.log('IPC handlers setup complete');
}

// Cleanup function to close database connections
export async function cleanupDatabase() {
  try {
    await pool.end();
    console.log('Database pool closed successfully');
  } catch (error) {
    console.error('Error closing database pool:', error);
  }
}